#include <opencv2/opencv.hpp>
#include <iostream>
#include <cstdlib>
#include <unistd.h>
#include <chrono>
#include <thread>

/**
 * @brief Wayland环境下的OpenCV GUI兼容性测试程序
 */
int main() {
    std::cout << "=== Wayland OpenCV GUI兼容性测试 ===" << std::endl;
    
    // 显示环境信息
    std::string display = std::getenv("DISPLAY") ? std::getenv("DISPLAY") : "未设置";
    std::string waylandDisplay = std::getenv("WAYLAND_DISPLAY") ? std::getenv("WAYLAND_DISPLAY") : "未设置";
    std::string sessionType = std::getenv("XDG_SESSION_TYPE") ? std::getenv("XDG_SESSION_TYPE") : "未设置";
    std::string gdkBackend = std::getenv("GDK_BACKEND") ? std::getenv("GDK_BACKEND") : "未设置";
    
    std::cout << "环境信息:" << std::endl;
    std::cout << "  DISPLAY = " << display << std::endl;
    std::cout << "  WAYLAND_DISPLAY = " << waylandDisplay << std::endl;
    std::cout << "  XDG_SESSION_TYPE = " << sessionType << std::endl;
    std::cout << "  GDK_BACKEND = " << gdkBackend << std::endl;
    std::cout << "  OpenCV版本: " << CV_VERSION << std::endl;
    std::cout << std::endl;
    
    // 检测Wayland环境
    bool isWayland = (sessionType == "wayland" || waylandDisplay != "未设置");
    if (isWayland) {
        std::cout << "🔍 检测到Wayland环境" << std::endl;
        
        // 应用Wayland兼容性修复
        if (gdkBackend == "未设置" || gdkBackend != "x11") {
            std::cout << "设置 GDK_BACKEND=x11 以提高兼容性..." << std::endl;
            setenv("GDK_BACKEND", "x11", 1);
        }
        
        if (display == "未设置") {
            std::cout << "设置 DISPLAY=:0..." << std::endl;
            setenv("DISPLAY", ":0", 1);
        }
        
        setenv("QT_QPA_PLATFORM", "xcb", 1);
        std::cout << "设置 QT_QPA_PLATFORM=xcb" << std::endl;
        std::cout << std::endl;
    }
    
    try {
        // 创建测试图像
        cv::Mat testImage = cv::Mat::zeros(400, 600, CV_8UC3);
        
        // 绘制内容
        cv::putText(testImage, "Wayland Compatibility Test", cv::Point(120, 80), 
                   cv::FONT_HERSHEY_SIMPLEX, 0.8, cv::Scalar(0, 255, 0), 2);
        
        if (isWayland) {
            cv::putText(testImage, "Running in Wayland environment", cv::Point(130, 120), 
                       cv::FONT_HERSHEY_SIMPLEX, 0.6, cv::Scalar(255, 255, 0), 1);
            cv::putText(testImage, "with X11 compatibility layer", cv::Point(140, 150), 
                       cv::FONT_HERSHEY_SIMPLEX, 0.6, cv::Scalar(255, 255, 0), 1);
        } else {
            cv::putText(testImage, "Running in X11 environment", cv::Point(150, 120), 
                       cv::FONT_HERSHEY_SIMPLEX, 0.6, cv::Scalar(255, 255, 255), 1);
        }
        
        cv::putText(testImage, "Window should stay open for 10 seconds", cv::Point(100, 200), 
                   cv::FONT_HERSHEY_SIMPLEX, 0.6, cv::Scalar(100, 200, 255), 1);
        cv::putText(testImage, "Press ESC to exit early", cv::Point(180, 230), 
                   cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(200, 200, 200), 1);
        
        // 绘制进度条背景
        cv::rectangle(testImage, cv::Point(50, 300), cv::Point(550, 320), 
                     cv::Scalar(100, 100, 100), -1);
        
        std::cout << "创建测试窗口..." << std::endl;
        
        // 创建窗口
        cv::namedWindow("Wayland Compatibility Test", cv::WINDOW_AUTOSIZE);
        
        std::cout << "✅ 窗口创建成功" << std::endl;
        std::cout << "测试窗口稳定性（10秒）..." << std::endl;
        
        // 测试窗口稳定性
        auto startTime = std::chrono::steady_clock::now();
        int frameCount = 0;
        
        while (true) {
            auto currentTime = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - startTime);
            
            if (elapsed.count() >= 10000) { // 10秒
                break;
            }
            
            // 更新进度条
            cv::Mat displayImage = testImage.clone();
            double progress = elapsed.count() / 10000.0;
            int progressWidth = static_cast<int>(500 * progress);
            
            cv::rectangle(displayImage, cv::Point(50, 300), cv::Point(50 + progressWidth, 320), 
                         cv::Scalar(0, 255, 0), -1);
            
            // 显示剩余时间
            int remainingSeconds = 10 - (elapsed.count() / 1000);
            cv::putText(displayImage, "Remaining: " + std::to_string(remainingSeconds) + "s", 
                       cv::Point(250, 280), cv::FONT_HERSHEY_SIMPLEX, 0.6, cv::Scalar(255, 255, 255), 1);
            
            cv::imshow("Wayland Compatibility Test", displayImage);
            
            // 检查按键
            int key = cv::waitKey(30) & 0xFF;
            if (key == 27) { // ESC
                std::cout << "用户按ESC退出" << std::endl;
                break;
            }
            
            frameCount++;
            
            // 每秒检查一次窗口状态
            if (frameCount % 33 == 0) {
                try {
                    double visible = cv::getWindowProperty("Wayland Compatibility Test", cv::WND_PROP_VISIBLE);
                    if (isWayland) {
                        // Wayland环境下，窗口属性可能不准确
                        std::cout << "帧 " << frameCount << ": 窗口属性=" << visible 
                                 << " (Wayland环境下可能不准确)" << std::endl;
                    } else {
                        std::cout << "帧 " << frameCount << ": 窗口属性=" << visible << std::endl;
                        if (visible < 1) {
                            std::cout << "❌ 窗口被关闭" << std::endl;
                            break;
                        }
                    }
                } catch (const cv::Exception& e) {
                    if (isWayland) {
                        std::cout << "帧 " << frameCount << ": 窗口属性检查异常 (Wayland环境正常): " << e.what() << std::endl;
                    } else {
                        std::cout << "❌ 窗口属性检查异常: " << e.what() << std::endl;
                        break;
                    }
                }
            }
        }
        
        cv::destroyAllWindows();
        
        auto totalTime = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - startTime);
        
        std::cout << std::endl;
        std::cout << "✅ 测试完成！" << std::endl;
        std::cout << "窗口运行时间: " << totalTime.count() << "ms" << std::endl;
        std::cout << "总帧数: " << frameCount << std::endl;
        
        if (totalTime.count() >= 9000) {
            std::cout << "🎉 Wayland兼容性测试通过！窗口稳定运行" << std::endl;
        } else {
            std::cout << "⚠️  窗口提前关闭，可能存在兼容性问题" << std::endl;
        }
        
        return 0;
        
    } catch (const cv::Exception& e) {
        std::cout << "❌ OpenCV错误: " << e.what() << std::endl;
        std::cout << std::endl;
        std::cout << "Wayland环境解决建议:" << std::endl;
        std::cout << "1. export GDK_BACKEND=x11" << std::endl;
        std::cout << "2. export QT_QPA_PLATFORM=xcb" << std::endl;
        std::cout << "3. 重启应用程序" << std::endl;
        return -1;
    }
}

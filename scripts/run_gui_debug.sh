#!/bin/bash

# GUI调试工具智能启动脚本
# 自动检测环境并应用必要的修复

echo "=== 缺损检测GUI调试工具启动器 ==="
echo "启动时间: $(date)"
echo

# 检查当前环境
DISPLAY_VAR="${DISPLAY:-}"
WAYLAND_DISPLAY_VAR="${WAYLAND_DISPLAY:-}"
SESSION_TYPE="${XDG_SESSION_TYPE:-}"
GDK_BACKEND_VAR="${GDK_BACKEND:-}"

echo "环境检测:"
echo "  DISPLAY = ${DISPLAY_VAR:-未设置}"
echo "  WAYLAND_DISPLAY = ${WAYLAND_DISPLAY_VAR:-未设置}"
echo "  XDG_SESSION_TYPE = ${SESSION_TYPE:-未设置}"
echo "  GDK_BACKEND = ${GDK_BACKEND_VAR:-未设置}"
echo

# 检查是否在图形环境中
if [ "$SESSION_TYPE" = "tty" ] || ([ -z "$DISPLAY_VAR" ] && [ -z "$WAYLAND_DISPLAY_VAR" ]); then
    echo "❌ 检测到当前在文本终端(TTY)模式"
    echo
    echo "请选择解决方案:"
    echo "1. 启动图形桌面环境"
    echo "2. 切换到已有的图形界面"
    echo "3. 使用命令行模式"
    echo "4. 退出"
    echo
    
    read -p "请选择 (1-4): " choice
    
    case $choice in
        1)
            echo "尝试启动图形桌面环境..."
            
            # 检查并启动显示管理器
            if systemctl is-active --quiet gdm3; then
                echo "GDM3已在运行"
            elif systemctl is-enabled --quiet gdm3; then
                echo "启动GDM3..."
                sudo systemctl start gdm3
            elif systemctl is-enabled --quiet lightdm; then
                echo "启动LightDM..."
                sudo systemctl start lightdm
            else
                echo "未找到可用的显示管理器"
                echo "请安装桌面环境: sudo apt install ubuntu-desktop-minimal"
                exit 1
            fi
            
            echo "✅ 图形界面启动命令已执行"
            echo "请切换到图形界面 (Ctrl+Alt+F7) 然后重新运行此脚本"
            exit 0
            ;;
            
        2)
            echo "请按以下快捷键切换到图形界面:"
            echo "  Ctrl+Alt+F1  或  Ctrl+Alt+F7"
            echo "然后在图形界面的终端中重新运行此脚本"
            exit 0
            ;;
            
        3)
            echo "启动命令行模式..."
            cd "$(dirname "$0")/../build"
            
            echo "可用的命令行选项:"
            echo "1. 处理单张图像: ./bin/debug_tool --single image.jpg"
            echo "2. 批量处理: ./bin/debug_tool --batch ./images"
            echo "3. 查看帮助: ./bin/debug_tool --help"
            echo
            
            read -p "请输入图像文件路径 (或按Enter查看帮助): " image_path
            
            if [ -z "$image_path" ]; then
                ./bin/debug_tool --help
            elif [ -f "$image_path" ]; then
                ./bin/debug_tool --single "$image_path"
            else
                echo "文件不存在: $image_path"
                ./bin/debug_tool --help
            fi
            exit 0
            ;;
            
        4)
            echo "退出启动器"
            exit 0
            ;;
            
        *)
            echo "无效选择"
            exit 1
            ;;
    esac
fi

# 在图形环境中运行
echo "✅ 检测到图形环境"

# 应用Wayland兼容性修复
if [ "$SESSION_TYPE" = "wayland" ] || [ -n "$WAYLAND_DISPLAY_VAR" ]; then
    echo "🔧 检测到Wayland环境，应用兼容性修复..."
    
    export GDK_BACKEND=x11
    export QT_QPA_PLATFORM=xcb
    
    if [ -z "$DISPLAY_VAR" ]; then
        export DISPLAY=:0
    fi
    
    echo "✓ 设置 GDK_BACKEND=x11"
    echo "✓ 设置 QT_QPA_PLATFORM=xcb"
    echo "✓ 确保 DISPLAY=$DISPLAY"
fi

# 检查程序是否存在
cd "$(dirname "$0")/.."
if [ ! -f "build/bin/debug_tool" ]; then
    echo "❌ 调试工具未找到，请先编译程序"
    echo "运行: cd build && make"
    exit 1
fi

echo
echo "🚀 启动GUI调试工具..."
echo "提示: 如果窗口没有出现，请检查任务栏或按Alt+Tab切换"
echo

# 启动程序
cd build
exec ./bin/debug_tool

#!/bin/bash

# 编译OpenCV GUI测试程序

echo "=== 编译OpenCV GUI测试程序 ==="

# 检查是否在正确的目录
if [ ! -f "test_opencv_gui.cpp" ]; then
    echo "错误: 找不到test_opencv_gui.cpp文件"
    echo "请在项目根目录运行此脚本"
    exit 1
fi

# 检查OpenCV
if ! pkg-config --exists opencv4; then
    echo "错误: 未找到OpenCV4"
    echo "请安装OpenCV: sudo apt install libopencv-dev"
    exit 1
fi

echo "OpenCV版本: $(pkg-config --modversion opencv4)"

# 编译
echo "编译中..."
g++ -std=c++17 test_opencv_gui.cpp -o test_opencv_gui \
    $(pkg-config --cflags --libs opencv4) \
    -pthread

if [ $? -eq 0 ]; then
    echo "✓ 编译成功"
    echo "运行测试: ./test_opencv_gui"
else
    echo "✗ 编译失败"
    exit 1
fi

#!/bin/bash

# RTSP服务器设置脚本
# 用于下载和配置MediaMTX服务器进行推流测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 读取配置文件中的网络设置（端口和IP地址）
read_config_settings() {
    local config_file="$1"

    # 默认端口值
    RTSP_PORT=8554
    RTMP_PORT=1935
    API_PORT=9997
    HLS_PORT=8888
    WEBRTC_PORT=8889
    METRICS_PORT=9998

    # 默认IP地址值
    SERVER_ADDRESS="127.0.0.1"
    API_BIND_ADDRESS="127.0.0.1"
    METRICS_BIND_ADDRESS="127.0.0.1"

    if [[ ! -f "$config_file" ]]; then
        log_warning "配置文件不存在: $config_file，使用默认网络配置"
        return 0
    fi

    log_info "从配置文件读取网络配置: $config_file"

    # 使用jq解析JSON配置（如果可用）
    if command -v jq &> /dev/null; then
        # 读取端口配置
        RTSP_PORT=$(jq -r '.streaming.rtsp_server_port // 8554' "$config_file" 2>/dev/null)
        API_PORT=$(jq -r '.network.api_port // 9997' "$config_file" 2>/dev/null)

        # 读取IP地址配置
        SERVER_ADDRESS=$(jq -r '.streaming.rtsp_server_address // "127.0.0.1"' "$config_file" 2>/dev/null)

        # MediaMTX特定端口配置（如果存在）
        if jq -e '.mediamtx' "$config_file" >/dev/null 2>&1; then
            RTMP_PORT=$(jq -r '.mediamtx.rtmp_port // 1935' "$config_file" 2>/dev/null)
            HLS_PORT=$(jq -r '.mediamtx.hls_port // 8888' "$config_file" 2>/dev/null)
            WEBRTC_PORT=$(jq -r '.mediamtx.webrtc_port // 8889' "$config_file" 2>/dev/null)
            METRICS_PORT=$(jq -r '.mediamtx.metrics_port // 9998' "$config_file" 2>/dev/null)

            # 检查是否有高级绑定地址配置（可选）
            local mediamtx_bind_addr=$(jq -r '.mediamtx.bind_address // null' "$config_file" 2>/dev/null)
            local api_bind_addr=$(jq -r '.mediamtx.api_bind_address // null' "$config_file" 2>/dev/null)
            local metrics_bind_addr=$(jq -r '.mediamtx.metrics_bind_address // null' "$config_file" 2>/dev/null)

            # 使用高级配置或默认使用服务器地址
            if [[ "$api_bind_addr" != "null" && -n "$api_bind_addr" ]]; then
                API_BIND_ADDRESS="$api_bind_addr"
            elif [[ "$mediamtx_bind_addr" != "null" && -n "$mediamtx_bind_addr" ]]; then
                API_BIND_ADDRESS="$mediamtx_bind_addr"
            else
                API_BIND_ADDRESS="$SERVER_ADDRESS"
            fi

            if [[ "$metrics_bind_addr" != "null" && -n "$metrics_bind_addr" ]]; then
                METRICS_BIND_ADDRESS="$metrics_bind_addr"
            elif [[ "$mediamtx_bind_addr" != "null" && -n "$mediamtx_bind_addr" ]]; then
                METRICS_BIND_ADDRESS="$mediamtx_bind_addr"
            else
                METRICS_BIND_ADDRESS="$SERVER_ADDRESS"
            fi
        else
            # 没有MediaMTX配置节时，使用服务器地址
            API_BIND_ADDRESS="$SERVER_ADDRESS"
            METRICS_BIND_ADDRESS="$SERVER_ADDRESS"
        fi
    else
        # 简单的grep解析作为备选方案
        local rtsp_port_line=$(grep -o '"rtsp_server_port"[[:space:]]*:[[:space:]]*[0-9]*' "$config_file" 2>/dev/null)
        if [[ -n "$rtsp_port_line" ]]; then
            RTSP_PORT=$(echo "$rtsp_port_line" | grep -o '[0-9]*$')
        fi

        local api_port_line=$(grep -o '"api_port"[[:space:]]*:[[:space:]]*[0-9]*' "$config_file" 2>/dev/null)
        if [[ -n "$api_port_line" ]]; then
            API_PORT=$(echo "$api_port_line" | grep -o '[0-9]*$')
        fi

        # 读取IP地址配置
        local server_addr_line=$(grep -o '"rtsp_server_address"[[:space:]]*:[[:space:]]*"[^"]*"' "$config_file" 2>/dev/null)
        if [[ -n "$server_addr_line" ]]; then
            SERVER_ADDRESS=$(echo "$server_addr_line" | sed 's/.*"\([^"]*\)".*/\1/')
        fi

        # 使用服务器地址作为绑定地址
        API_BIND_ADDRESS="$SERVER_ADDRESS"
        METRICS_BIND_ADDRESS="$SERVER_ADDRESS"
    fi

    # 验证端口号有效性
    for port in RTSP_PORT RTMP_PORT API_PORT HLS_PORT WEBRTC_PORT METRICS_PORT; do
        local port_value=${!port}
        if [[ ! "$port_value" =~ ^[0-9]+$ ]] || [[ "$port_value" -lt 1024 ]] || [[ "$port_value" -gt 65535 ]]; then
            log_warning "端口 $port 值无效: $port_value，使用默认值"
            case $port in
                RTSP_PORT) RTSP_PORT=8554 ;;
                RTMP_PORT) RTMP_PORT=1935 ;;
                API_PORT) API_PORT=9997 ;;
                HLS_PORT) HLS_PORT=8888 ;;
                WEBRTC_PORT) WEBRTC_PORT=8889 ;;
                METRICS_PORT) METRICS_PORT=9998 ;;
            esac
        fi
    done

    # 验证IP地址有效性（简单验证）
    validate_ip_address() {
        local ip="$1"
        if [[ "$ip" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
            return 0
        else
            return 1
        fi
    }

    for addr_var in SERVER_ADDRESS API_BIND_ADDRESS METRICS_BIND_ADDRESS; do
        local addr_value=${!addr_var}
        if ! validate_ip_address "$addr_value"; then
            log_warning "IP地址 $addr_var 格式无效: $addr_value，使用默认值 127.0.0.1"
            case $addr_var in
                SERVER_ADDRESS) SERVER_ADDRESS="127.0.0.1" ;;
                API_BIND_ADDRESS) API_BIND_ADDRESS="127.0.0.1" ;;
                METRICS_BIND_ADDRESS) METRICS_BIND_ADDRESS="127.0.0.1" ;;
            esac
        fi
    done

    log_info "网络配置读取完成:"
    log_info "  服务器地址: $SERVER_ADDRESS"
    log_info "  RTSP端口: $RTSP_PORT"
    log_info "  RTMP端口: $RTMP_PORT"
    log_info "  API绑定: $API_BIND_ADDRESS:$API_PORT"
    log_info "  HLS端口: $HLS_PORT"
    log_info "  WebRTC端口: $WEBRTC_PORT"
    log_info "  指标绑定: $METRICS_BIND_ADDRESS:$METRICS_PORT"
}

# 检测系统架构
detect_architecture() {
    local arch=$(uname -m)
    case $arch in
        x86_64)
            echo "amd64"
            ;;
        aarch64|arm64)
            echo "arm64"
            ;;
        armv7l)
            echo "armv7"
            ;;
        *)
            log_error "不支持的架构: $arch"
            exit 1
            ;;
    esac
}

# 下载MediaMTX
download_mediamtx() {
    local version="v1.5.1"
    local arch=$(detect_architecture)
    local os="linux"
    local filename="mediamtx_${version}_${os}_${arch}.tar.gz"
    local download_url="https://github.com/bluenviron/mediamtx/releases/download/${version}/${filename}"
    
    log_info "下载MediaMTX ${version} for ${os}_${arch}..."
    
    # 创建临时目录
    local temp_dir=$(mktemp -d)
    cd "$temp_dir"
    
    # 下载文件
    if command -v wget &> /dev/null; then
        wget -q --show-progress "$download_url" || {
            log_error "下载失败，请检查网络连接"
            exit 1
        }
    elif command -v curl &> /dev/null; then
        curl -L -o "$filename" "$download_url" || {
            log_error "下载失败，请检查网络连接"
            exit 1
        }
    else
        log_error "需要wget或curl来下载文件"
        exit 1
    fi
    
    # 解压文件
    log_info "解压MediaMTX..."
    tar -xzf "$filename"
    
    # 移动到项目目录
    local project_dir="/home/<USER>/project/fault_detect"
    mv mediamtx "$project_dir/"
    mv mediamtx.yml "$project_dir/"
    
    # 清理临时文件
    cd "$project_dir"
    rm -rf "$temp_dir"
    
    # 设置执行权限
    chmod +x mediamtx
    
    log_success "MediaMTX下载完成"
}

# 配置MediaMTX
configure_mediamtx() {
    log_info "配置MediaMTX..."

    # 创建自定义配置文件，使用动态端口配置
    cat > mediamtx_test.yml << EOF
# MediaMTX测试配置文件

# 日志级别
logLevel: info
logDestinations: [stdout]

# API配置
api: yes
apiAddress: ${API_BIND_ADDRESS}:${API_PORT}

# 指标配置
metrics: yes
metricsAddress: ${METRICS_BIND_ADDRESS}:${METRICS_PORT}

# RTSP服务器配置
rtsp: yes
protocols: [udp, multicast, tcp]
rtspAddress: :${RTSP_PORT}
rtpAddress: :8000
rtcpAddress: :8001
multicastIPRange: *********/16
multicastRTPPort: 8002
multicastRTCPPort: 8003
encryption: "no"
serverKey: server.key
serverCert: server.crt
authMethods: [basic]

# RTMP服务器配置
rtmpAddress: :${RTMP_PORT}
rtmpEncryption: "no"
rtmpServerKey: server.key
rtmpServerCert: server.crt

# HLS配置
hlsAddress: :${HLS_PORT}
hlsEncryption: no
hlsServerKey: server.key
hlsServerCert: server.crt
hlsAlwaysRemux: no
hlsVariant: lowLatency
hlsSegmentCount: 7
hlsSegmentDuration: 1s
hlsPartDuration: 200ms
hlsSegmentMaxSize: 50M

# WebRTC配置
webrtcAddress: :${WEBRTC_PORT}
webrtcEncryption: no
webrtcServerKey: server.key
webrtcServerCert: server.crt

# 路径配置
paths:
  # 测试路径
  live:
    # 允许发布
    publishUser: ""
    publishPass: ""
    publishIPs: []
    
    # 允许读取
    readUser: ""
    readPass: ""
    readIPs: []
    
    # 运行时参数
    runOnInit: ""
    runOnInitRestart: no
    runOnDemand: ""
    runOnDemandRestart: no
    runOnDemandStartTimeout: 10s
    runOnDemandCloseAfter: 10s
    runOnUnDemand: ""
    
    # 源配置
    source: publisher
    sourceFingerprint: ""
    sourceOnDemand: no
    sourceOnDemandStartTimeout: 10s
    sourceOnDemandCloseAfter: 10s
    sourceRedirect: ""
    
    # 发布者配置
    disablePublisherOverride: no
    fallback: ""
    
    # SRT配置
    srtPublishPassphrase: ""
    srtReadPassphrase: ""
    
  # 所有其他路径
  "~^.*":
    publishUser: ""
    publishPass: ""
    readUser: ""
    readPass: ""
EOF
    
    log_success "MediaMTX配置完成"
}

# 启动MediaMTX
start_mediamtx() {
    log_info "启动MediaMTX服务器..."
    
    # 检查端口是否被占用
    if netstat -tuln 2>/dev/null | grep -q ":8554 "; then
        log_warning "端口8554已被占用，尝试停止现有服务..."
        pkill -f mediamtx || true
        sleep 2
    fi
    
    if netstat -tuln 2>/dev/null | grep -q ":1935 "; then
        log_warning "端口1935已被占用，尝试停止现有服务..."
        pkill -f mediamtx || true
        sleep 2
    fi
    
    # 启动MediaMTX（后台运行）
    ./mediamtx mediamtx_test.yml > mediamtx.log 2>&1 &
    local mediamtx_pid=$!
    
    # 等待服务启动
    log_info "等待MediaMTX启动..."
    sleep 3
    
    # 检查服务是否正常运行
    if kill -0 $mediamtx_pid 2>/dev/null; then
        log_success "MediaMTX启动成功 (PID: $mediamtx_pid)"
        echo $mediamtx_pid > mediamtx.pid
        
        # 显示服务信息
        log_info "服务信息:"
        log_info "  服务器地址: $SERVER_ADDRESS"
        log_info "  RTSP端口: $RTSP_PORT"
        log_info "  RTMP端口: $RTMP_PORT"
        log_info "  API服务: $API_BIND_ADDRESS:$API_PORT"
        log_info "  HLS端口: $HLS_PORT"
        log_info "  WebRTC端口: $WEBRTC_PORT"
        log_info "  指标服务: $METRICS_BIND_ADDRESS:$METRICS_PORT"
        log_info "  推流地址: rtmp://$SERVER_ADDRESS:$RTMP_PORT/live"
        log_info "  观看地址: rtsp://$SERVER_ADDRESS:$RTSP_PORT/live"
        
        return 0
    else
        log_error "MediaMTX启动失败"
        cat mediamtx.log
        return 1
    fi
}

# 停止MediaMTX
stop_mediamtx() {
    log_info "停止MediaMTX服务器..."
    
    if [ -f mediamtx.pid ]; then
        local pid=$(cat mediamtx.pid)
        if kill -0 $pid 2>/dev/null; then
            kill $pid
            sleep 2
            if kill -0 $pid 2>/dev/null; then
                kill -9 $pid
            fi
        fi
        rm -f mediamtx.pid
    fi
    
    # 确保所有mediamtx进程都被停止
    pkill -f mediamtx || true
    
    log_success "MediaMTX已停止"
}

# 检查MediaMTX状态
check_mediamtx() {
    log_info "检查MediaMTX状态..."
    
    if [ -f mediamtx.pid ]; then
        local pid=$(cat mediamtx.pid)
        if kill -0 $pid 2>/dev/null; then
            log_success "MediaMTX正在运行 (PID: $pid)"
            
            # 检查端口
            if netstat -tuln 2>/dev/null | grep -q ":8554 "; then
                log_success "RTSP端口8554正常监听"
            else
                log_warning "RTSP端口8554未监听"
            fi
            
            if netstat -tuln 2>/dev/null | grep -q ":1935 "; then
                log_success "RTMP端口1935正常监听"
            else
                log_warning "RTMP端口1935未监听"
            fi
            
            return 0
        else
            log_warning "MediaMTX进程不存在"
            rm -f mediamtx.pid
            return 1
        fi
    else
        log_warning "MediaMTX未运行"
        return 1
    fi
}

# 主函数
main() {
    # 确定配置文件路径
    local config_file=""
    if [[ -f "../config/system_config.json" ]]; then
        config_file="../config/system_config.json"
    elif [[ -f "config/system_config.json" ]]; then
        config_file="config/system_config.json"
    elif [[ -f "/home/<USER>/project/fault_detect/config/system_config.json" ]]; then
        config_file="/home/<USER>/project/fault_detect/config/system_config.json"
    fi

    # 读取配置文件中的网络设置（端口和IP地址）
    read_config_settings "$config_file"

    case "${1:-setup}" in
        setup)
            log_info "=== MediaMTX RTSP服务器设置 ==="

            # 检查是否已存在
            if [ -f mediamtx ]; then
                log_info "MediaMTX已存在，跳过下载"
            else
                download_mediamtx
            fi

            configure_mediamtx
            start_mediamtx
            ;;
        start)
            start_mediamtx
            ;;
        stop)
            stop_mediamtx
            ;;
        status)
            check_mediamtx
            ;;
        restart)
            stop_mediamtx
            sleep 1
            start_mediamtx
            ;;
        *)
            echo "用法: $0 {setup|start|stop|status|restart}"
            echo "  setup   - 下载、配置并启动MediaMTX"
            echo "  start   - 启动MediaMTX"
            echo "  stop    - 停止MediaMTX"
            echo "  status  - 检查MediaMTX状态"
            echo "  restart - 重启MediaMTX"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"

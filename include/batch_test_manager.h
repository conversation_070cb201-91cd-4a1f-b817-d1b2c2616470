#pragma once

#include "common.h"
#include "damage_detection_engine.h"
#include "visualization_debugger.h"
#ifdef USE_OPENCV
#include <opencv2/opencv.hpp>
#include <vector>
#include <string>
#include <memory>
#include <map>

/**
 * @brief 批量测试管理器
 * 
 * 提供批量图像处理、结果对比分析和性能统计功能
 */
class BatchTestManager {
public:
    /**
     * @brief 测试结果结构
     */
    struct TestResult {
        std::string imagePath;
        std::vector<DamageResult> detectionResults;
        double processingTime;
        bool success;
        std::string errorMessage;
        
        TestResult() : processingTime(0.0), success(false) {}
    };
    
    /**
     * @brief 批量测试统计信息
     */
    struct BatchStatistics {
        int totalImages = 0;
        int successfulImages = 0;
        int failedImages = 0;
        int totalDamagesDetected = 0;
        double totalProcessingTime = 0.0;
        double averageProcessingTime = 0.0;
        double averageConfidence = 0.0;
        
        // 各类型损伤统计
        std::map<DamageType, int> damageTypeCounts;
        std::map<DamageType, double> averageConfidenceByType;
        
        // 性能统计
        double minProcessingTime = std::numeric_limits<double>::max();
        double maxProcessingTime = 0.0;
        
        void reset() {
            totalImages = 0;
            successfulImages = 0;
            failedImages = 0;
            totalDamagesDetected = 0;
            totalProcessingTime = 0.0;
            averageProcessingTime = 0.0;
            averageConfidence = 0.0;
            damageTypeCounts.clear();
            averageConfidenceByType.clear();
            minProcessingTime = std::numeric_limits<double>::max();
            maxProcessingTime = 0.0;
        }
    };
    
    /**
     * @brief 对比测试结果
     */
    struct ComparisonResult {
        std::string testName1;
        std::string testName2;
        BatchStatistics stats1;
        BatchStatistics stats2;
        
        // 对比指标
        double processingTimeRatio;  // stats2/stats1
        double detectionCountRatio;  // stats2/stats1
        double confidenceRatio;      // stats2/stats1
        
        std::map<DamageType, double> typeCountRatios;
    };

public:
    BatchTestManager();
    ~BatchTestManager();

    /**
     * @brief 初始化批量测试管理器
     * @param engine 检测引擎
     * @param debugger 可视化调试器（可选）
     * @return 初始化是否成功
     */
    bool initialize(std::shared_ptr<DamageDetectionEngine> engine,
                   std::shared_ptr<VisualizationDebugger> debugger = nullptr);

    /**
     * @brief 执行批量测试
     * @param imagePaths 图像文件路径列表
     * @param outputDir 输出目录
     * @param testName 测试名称
     * @return 测试结果列表
     */
    std::vector<TestResult> runBatchTest(const std::vector<std::string>& imagePaths,
                                        const std::string& outputDir,
                                        const std::string& testName = "batch_test");

    /**
     * @brief 执行参数对比测试
     * @param imagePaths 图像文件路径列表
     * @param params1 第一组参数
     * @param params2 第二组参数
     * @param outputDir 输出目录
     * @return 对比结果
     */
    ComparisonResult runComparisonTest(const std::vector<std::string>& imagePaths,
                                      const DamageDetectionEngine::AlgorithmParams& params1,
                                      const DamageDetectionEngine::AlgorithmParams& params2,
                                      const std::string& outputDir);

    /**
     * @brief 生成测试报告
     * @param results 测试结果
     * @param stats 统计信息
     * @param outputPath 报告输出路径
     * @return 生成是否成功
     */
    bool generateTestReport(const std::vector<TestResult>& results,
                           const BatchStatistics& stats,
                           const std::string& outputPath);

    /**
     * @brief 生成对比报告
     * @param comparison 对比结果
     * @param outputPath 报告输出路径
     * @return 生成是否成功
     */
    bool generateComparisonReport(const ComparisonResult& comparison,
                                 const std::string& outputPath);

    /**
     * @brief 计算批量统计信息
     * @param results 测试结果列表
     * @return 统计信息
     */
    BatchStatistics calculateStatistics(const std::vector<TestResult>& results);

    /**
     * @brief 保存测试结果到CSV文件
     * @param results 测试结果
     * @param outputPath CSV文件路径
     * @return 保存是否成功
     */
    bool saveResultsToCSV(const std::vector<TestResult>& results,
                         const std::string& outputPath);

    /**
     * @brief 从目录加载图像文件
     * @param directory 目录路径
     * @param recursive 是否递归搜索子目录
     * @return 图像文件路径列表
     */
    static std::vector<std::string> loadImagesFromDirectory(const std::string& directory,
                                                           bool recursive = false);

    /**
     * @brief 创建测试配置文件
     * @param configPath 配置文件路径
     * @param params 算法参数
     * @return 创建是否成功
     */
    bool createTestConfig(const std::string& configPath,
                         const DamageDetectionEngine::AlgorithmParams& params);

    /**
     * @brief 从配置文件加载参数
     * @param configPath 配置文件路径
     * @return 算法参数
     */
    DamageDetectionEngine::AlgorithmParams loadTestConfig(const std::string& configPath);

private:
    std::shared_ptr<DamageDetectionEngine> engine_;
    std::shared_ptr<VisualizationDebugger> debugger_;
    
    // 内部辅助方法
    TestResult processImage(const std::string& imagePath);
    void updateStatistics(BatchStatistics& stats, const TestResult& result);
    ComparisonResult compareStatistics(const BatchStatistics& stats1,
                                      const BatchStatistics& stats2,
                                      const std::string& name1,
                                      const std::string& name2);
    
    // 文件操作辅助方法
    bool isImageFile(const std::string& filename);
    static bool isImageFileStatic(const std::string& filename);
    std::string formatDuration(double milliseconds);
    std::string getCurrentTimestamp();
    
    // 报告生成辅助方法
    void writeHTMLReportHeader(std::ofstream& file, const std::string& title);
    void writeHTMLReportFooter(std::ofstream& file);
    void writeStatisticsTable(std::ofstream& file, const BatchStatistics& stats);
    void writeComparisonTable(std::ofstream& file, const ComparisonResult& comparison);
};

/**
 * @brief 测试配置管理器
 */
class TestConfigManager {
public:
    /**
     * @brief 预定义测试配置
     */
    enum class PresetConfig {
        DEFAULT,        // 默认参数
        HIGH_PRECISION, // 高精度模式
        HIGH_SPEED,     // 高速度模式
        CRACK_FOCUSED,  // 专注裂缝检测
        WEAR_FOCUSED    // 专注磨损检测
    };
    
    /**
     * @brief 获取预定义配置
     * @param preset 预设类型
     * @return 算法参数
     */
    static DamageDetectionEngine::AlgorithmParams getPresetConfig(PresetConfig preset);
    
    /**
     * @brief 创建参数变化序列（用于参数敏感性分析）
     * @param baseParams 基础参数
     * @param paramName 要变化的参数名
     * @param minValue 最小值
     * @param maxValue 最大值
     * @param steps 步数
     * @return 参数序列
     */
    static std::vector<DamageDetectionEngine::AlgorithmParams> 
    createParameterSequence(const DamageDetectionEngine::AlgorithmParams& baseParams,
                           const std::string& paramName,
                           double minValue, double maxValue, int steps);
};

#endif // USE_OPENCV

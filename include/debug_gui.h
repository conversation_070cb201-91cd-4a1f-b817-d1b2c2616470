#pragma once

#include "common.h"
#include "visualization_debugger.h"
#ifdef USE_OPENCV
#include <opencv2/opencv.hpp>
#include <vector>
#include <string>
#include <memory>
#include <functional>

/**
 * @brief 调试GUI界面类
 * 
 * 基于OpenCV的GUI界面，提供：
 * - 图像加载和显示
 * - 参数调整控件
 * - 损伤类型选择
 * - 批量处理控制
 * - 结果保存功能
 */
class DebugGUI {
public:
    /**
     * @brief GUI控件状态结构
     */
    struct GUIState {
        // 文件相关
        std::string currentImagePath;
        std::vector<std::string> imageList;
        int currentImageIndex = 0;
        
        // 显示控制
        std::vector<DamageType> enabledDamageTypes = {
            DamageType::CRACK, DamageType::WEAR, DamageType::SCRATCH,
            DamageType::PIT, DamageType::BULGE, DamageType::AGING,
            DamageType::INSTALL_DAMAGE
        };
        double confidenceThreshold = 0.3;
        
        // 算法参数
        double crackMinLength = 5.0;
        double crackMaxWidth = 3.0;
        double wearAreaThreshold = 100.0;
        double dentMinArea = 50.0;
        double bulgeMinArea = 50.0;
        
        // 显示选项
        bool showBoundingBox = true;
        bool showConfidence = true;
        bool showDamageType = true;
        bool showCenter = false;
        
        // 调试模式
        VisualizationDebugger::DebugMode debugMode = VisualizationDebugger::DebugMode::NORMAL;
        
        // 批量处理
        std::string batchInputDir;
        std::string batchOutputDir = "output/debug_results";
        bool batchProcessing = false;
    };

    /**
     * @brief 回调函数类型定义
     */
    using ParameterChangeCallback = std::function<void(const GUIState&)>;
    using ImageChangeCallback = std::function<void(const std::string&)>;
    using BatchProcessCallback = std::function<void(const std::vector<std::string>&, const std::string&)>;

public:
    DebugGUI();
    ~DebugGUI();

    /**
     * @brief 初始化GUI
     * @param debugger 可视化调试器指针
     * @return 初始化是否成功
     */
    bool initialize(std::shared_ptr<VisualizationDebugger> debugger);

    /**
     * @brief 运行GUI主循环
     * @return 退出代码
     */
    int run();

    /**
     * @brief 设置参数变化回调
     * @param callback 回调函数
     */
    void setParameterChangeCallback(ParameterChangeCallback callback);

    /**
     * @brief 设置图像变化回调
     * @param callback 回调函数
     */
    void setImageChangeCallback(ImageChangeCallback callback);

    /**
     * @brief 设置批量处理回调
     * @param callback 回调函数
     */
    void setBatchProcessCallback(BatchProcessCallback callback);

    /**
     * @brief 加载图像文件
     * @param imagePath 图像文件路径
     * @return 加载是否成功
     */
    bool loadImage(const std::string& imagePath);

    /**
     * @brief 加载图像目录
     * @param dirPath 目录路径
     * @return 加载的图像数量
     */
    int loadImageDirectory(const std::string& dirPath);

    /**
     * @brief 获取当前GUI状态
     * @return GUI状态
     */
    const GUIState& getState() const { return state_; }

    /**
     * @brief 更新显示
     */
    void updateDisplay();

private:
    std::shared_ptr<VisualizationDebugger> debugger_;
    GUIState state_;
    
    // 回调函数
    ParameterChangeCallback parameterChangeCallback_;
    ImageChangeCallback imageChangeCallback_;
    BatchProcessCallback batchProcessCallback_;
    
    // GUI窗口名称
    static const std::string MAIN_WINDOW;
    static const std::string CONTROL_WINDOW;
    static const std::string PARAM_WINDOW;
    
    // 控件状态
    bool windowsCreated_ = false;
    
    // 内部方法
    void createWindows();
    void createControlPanel();
    void createParameterPanel();
    void setupTrackbars();
    void setupButtons();
    cv::Mat createWelcomeImage();
    
    // 事件处理
    static void onConfidenceThresholdChange(int value, void* userdata);
    static void onCrackMinLengthChange(int value, void* userdata);
    static void onCrackMaxWidthChange(int value, void* userdata);
    static void onWearAreaThresholdChange(int value, void* userdata);
    static void onDentMinAreaChange(int value, void* userdata);
    static void onBulgeMinAreaChange(int value, void* userdata);
    
    // 按钮事件处理
    void handleLoadImage();
    void handleLoadDirectory();
    void handlePreviousImage();
    void handleNextImage();
    void handleSaveResult();
    void handleBatchProcess();
    void handleResetParameters();
    void handleToggleDamageType(DamageType type);
    void handleDebugModeChange();
    
    // 辅助方法
    void updateParametersFromGUI();
    void updateGUIFromParameters();
    void notifyParameterChange();
    void notifyImageChange();
    std::vector<std::string> getImageFiles(const std::string& dirPath);
    bool isImageFile(const std::string& filename);
    
    // 创建控制面板图像
    cv::Mat createControlPanelImage();
    cv::Mat createParameterPanelImage();
    
    // 文件对话框（简化实现）
    std::string openFileDialog();
    std::string openDirectoryDialog();
    std::string saveFileDialog();
};

/**
 * @brief 简单的文件浏览器实现
 */
class SimpleFileBrowser {
public:
    static std::string selectFile(const std::string& title = "Select File",
                                 const std::string& initialDir = ".");
    static std::string selectDirectory(const std::string& title = "Select Directory",
                                      const std::string& initialDir = ".");
    static std::vector<std::string> getImageFiles(const std::string& directory);
    
private:
    static bool isImageFile(const std::string& filename);
};

#endif // USE_OPENCV

#include "../../include/common.h"
#include "../../include/damage_detection_engine.h"
#include "../../include/visualization_debugger.h"
#include "../../include/debug_gui.h"
#include "../../include/batch_test_manager.h"

#ifdef USE_OPENCV
#include <iostream>
#include <memory>
#include <string>
#include <vector>

/**
 * @brief 调试工具主程序
 * 
 * 整合所有调试功能的独立可执行程序
 */
class DebugToolApplication {
public:
    DebugToolApplication() = default;
    ~DebugToolApplication() = default;

    /**
     * @brief 初始化应用程序
     * @return 初始化是否成功
     */
    bool initialize() {
        Utils::logInfo("=== 缺损检测可视化调试工具 ===");
        Utils::logInfo("初始化调试工具...");
        
        // 初始化配置
        if (!Config::initializeConfig()) {
            Utils::logError("配置初始化失败");
            return false;
        }
        
        // 创建检测引擎
        engine_ = std::make_shared<DamageDetectionEngine>();
        if (!engine_->initialize()) {
            Utils::logError("检测引擎初始化失败");
            return false;
        }
        
        // 启用调试模式
        engine_->setDebugMode(true);
        
        // 创建可视化调试器
        debugger_ = std::make_shared<VisualizationDebugger>();
        if (!debugger_->initialize(engine_)) {
            Utils::logError("可视化调试器初始化失败");
            return false;
        }
        
        // 创建GUI界面
        gui_ = std::make_shared<DebugGUI>();
        if (!gui_->initialize(debugger_)) {
            Utils::logError("GUI界面初始化失败");
            return false;
        }
        
        // 创建批量测试管理器
        batchManager_ = std::make_shared<BatchTestManager>();
        if (!batchManager_->initialize(engine_, debugger_)) {
            Utils::logError("批量测试管理器初始化失败");
            return false;
        }
        
        // 设置回调函数
        setupCallbacks();
        
        Utils::logInfo("调试工具初始化完成");
        return true;
    }

    /**
     * @brief 运行应用程序
     * @param argc 命令行参数数量
     * @param argv 命令行参数
     * @return 退出代码
     */
    int run(int argc, char* argv[]) {
        // 解析命令行参数
        if (argc > 1) {
            return runCommandLine(argc, argv);
        } else {
            return runGUI();
        }
    }

private:
    std::shared_ptr<DamageDetectionEngine> engine_;
    std::shared_ptr<VisualizationDebugger> debugger_;
    std::shared_ptr<DebugGUI> gui_;
    std::shared_ptr<BatchTestManager> batchManager_;

    /**
     * @brief 设置回调函数
     */
    void setupCallbacks() {
        // 参数变化回调
        gui_->setParameterChangeCallback([this](const DebugGUI::GUIState& state) {
            onParameterChange(state);
        });
        
        // 图像变化回调
        gui_->setImageChangeCallback([this](const std::string& imagePath) {
            onImageChange(imagePath);
        });
        
        // 批量处理回调
        gui_->setBatchProcessCallback([this](const std::vector<std::string>& imagePaths, 
                                            const std::string& outputDir) {
            onBatchProcess(imagePaths, outputDir);
        });
    }

    /**
     * @brief 参数变化处理
     * @param state GUI状态
     */
    void onParameterChange(const DebugGUI::GUIState& state) {
        // 更新检测引擎参数
        auto params = engine_->getAlgorithmParams();
        params.crackMinLength = state.crackMinLength;
        params.crackMaxWidth = state.crackMaxWidth;
        params.wearAreaThreshold = state.wearAreaThreshold;
        params.dentMinArea = state.dentMinArea;
        params.bulgeMinArea = state.bulgeMinArea;
        engine_->setAlgorithmParams(params);
        
        // 更新可视化配置
        auto vizConfig = debugger_->getVisualizationConfig();
        vizConfig.confidenceThreshold = state.confidenceThreshold;
        vizConfig.showBoundingBox = state.showBoundingBox;
        vizConfig.showConfidence = state.showConfidence;
        vizConfig.showDamageType = state.showDamageType;
        vizConfig.showCenter = state.showCenter;
        debugger_->setVisualizationConfig(vizConfig);
        
        // 更新调试模式
        debugger_->setDebugMode(state.debugMode);
        
        // 如果有当前图像，重新处理
        if (!state.currentImagePath.empty()) {
            debugger_->loadAndProcess(state.currentImagePath);
        }
    }

    /**
     * @brief 图像变化处理
     * @param imagePath 图像路径
     */
    void onImageChange(const std::string& imagePath) {
        Utils::logInfo("加载图像: " + imagePath);
        debugger_->loadAndProcess(imagePath);
    }

    /**
     * @brief 批量处理处理
     * @param imagePaths 图像路径列表
     * @param outputDir 输出目录
     */
    void onBatchProcess(const std::vector<std::string>& imagePaths, 
                       const std::string& outputDir) {
        Utils::logInfo("开始批量处理，图像数量: " + std::to_string(imagePaths.size()));
        
        auto results = batchManager_->runBatchTest(imagePaths, outputDir, "gui_batch_test");
        
        Utils::logInfo("批量处理完成，结果保存到: " + outputDir);
    }

    /**
     * @brief 运行GUI模式
     * @return 退出代码
     */
    int runGUI() {
        Utils::logInfo("启动GUI模式");
        Utils::logInfo("使用说明:");
        Utils::logInfo("- 按 O 键打开图像文件");
        Utils::logInfo("- 按 D 键打开图像目录");
        Utils::logInfo("- 按 P/N 键切换上一张/下一张图像");
        Utils::logInfo("- 按 S 键保存当前结果");
        Utils::logInfo("- 按 B 键开始批量处理");
        Utils::logInfo("- 按 M 键切换调试模式");
        Utils::logInfo("- 按 1-7 键切换不同损伤类型的显示");
        Utils::logInfo("- 按 ESC 键退出");
        Utils::logInfo("- 使用滑动条调整检测参数");
        
        return gui_->run();
    }

    /**
     * @brief 运行命令行模式
     * @param argc 参数数量
     * @param argv 参数列表
     * @return 退出代码
     */
    int runCommandLine(int argc, char* argv[]) {
        std::string command = argv[1];
        
        if (command == "--help" || command == "-h") {
            printHelp();
            return 0;
        } else if (command == "--batch" || command == "-b") {
            return runBatchMode(argc, argv);
        } else if (command == "--single" || command == "-s") {
            return runSingleMode(argc, argv);
        } else if (command == "--compare" || command == "-c") {
            return runCompareMode(argc, argv);
        } else {
            Utils::logError("未知命令: " + command);
            printHelp();
            return 1;
        }
    }

    /**
     * @brief 打印帮助信息
     */
    void printHelp() {
        std::cout << "缺损检测可视化调试工具\n\n";
        std::cout << "用法:\n";
        std::cout << "  debug_tool                    # 启动GUI模式\n";
        std::cout << "  debug_tool --help             # 显示帮助信息\n";
        std::cout << "  debug_tool --single <image>   # 处理单张图像\n";
        std::cout << "  debug_tool --batch <dir>      # 批量处理目录中的图像\n";
        std::cout << "  debug_tool --compare <dir>    # 参数对比测试\n\n";
        std::cout << "选项:\n";
        std::cout << "  -h, --help     显示帮助信息\n";
        std::cout << "  -s, --single   单张图像模式\n";
        std::cout << "  -b, --batch    批量处理模式\n";
        std::cout << "  -c, --compare  参数对比模式\n\n";
        std::cout << "示例:\n";
        std::cout << "  debug_tool --single test.jpg\n";
        std::cout << "  debug_tool --batch ./test_images\n";
        std::cout << "  debug_tool --compare ./test_images\n";
    }

    /**
     * @brief 运行单张图像模式
     * @param argc 参数数量
     * @param argv 参数列表
     * @return 退出代码
     */
    int runSingleMode(int argc, char* argv[]) {
        if (argc < 3) {
            Utils::logError("缺少图像文件参数");
            return 1;
        }
        
        std::string imagePath = argv[2];
        Utils::logInfo("处理单张图像: " + imagePath);
        
        if (!debugger_->loadAndProcess(imagePath)) {
            Utils::logError("处理图像失败");
            return 1;
        }
        
        // 保存结果
        std::string outputPath = "output/debug_results/single_result.jpg";
        if (debugger_->saveCurrentResult(outputPath)) {
            Utils::logInfo("结果已保存: " + outputPath);
        }
        
        // 显示结果统计
        const auto& results = debugger_->getLastResults();
        Utils::logInfo("检测到 " + std::to_string(results.size()) + " 个损伤");
        
        for (const auto& result : results) {
            Utils::logInfo("- " + Utils::damageTypeToString(result.type) + 
                          " (置信度: " + std::to_string(result.confidence) + ")");
        }
        
        return 0;
    }

    /**
     * @brief 运行批量处理模式
     * @param argc 参数数量
     * @param argv 参数列表
     * @return 退出代码
     */
    int runBatchMode(int argc, char* argv[]) {
        if (argc < 3) {
            Utils::logError("缺少目录参数");
            return 1;
        }
        
        std::string inputDir = argv[2];
        std::string outputDir = "output/debug_results/batch_" + 
                               std::to_string(std::time(nullptr));
        
        Utils::logInfo("批量处理目录: " + inputDir);
        Utils::logInfo("输出目录: " + outputDir);
        
        // 加载图像文件
        auto imagePaths = BatchTestManager::loadImagesFromDirectory(inputDir, true);
        if (imagePaths.empty()) {
            Utils::logError("目录中没有找到图像文件");
            return 1;
        }
        
        // 执行批量测试
        auto results = batchManager_->runBatchTest(imagePaths, outputDir, "batch_test");
        
        Utils::logInfo("批量处理完成");
        return 0;
    }

    /**
     * @brief 运行参数对比模式
     * @param argc 参数数量
     * @param argv 参数列表
     * @return 退出代码
     */
    int runCompareMode(int argc, char* argv[]) {
        if (argc < 3) {
            Utils::logError("缺少目录参数");
            return 1;
        }
        
        std::string inputDir = argv[2];
        Utils::logInfo("参数对比测试目录: " + inputDir);
        
        // 加载图像文件
        auto imagePaths = BatchTestManager::loadImagesFromDirectory(inputDir, true);
        if (imagePaths.empty()) {
            Utils::logError("目录中没有找到图像文件");
            return 1;
        }
        
        // 使用预设配置进行对比
        auto defaultParams = TestConfigManager::getPresetConfig(TestConfigManager::PresetConfig::DEFAULT);
        auto highPrecisionParams = TestConfigManager::getPresetConfig(TestConfigManager::PresetConfig::HIGH_PRECISION);
        
        std::string outputDir = "output/debug_results/compare_" + 
                               std::to_string(std::time(nullptr));
        
        auto comparison = batchManager_->runComparisonTest(imagePaths, defaultParams, 
                                                          highPrecisionParams, outputDir);
        
        Utils::logInfo("参数对比测试完成，结果保存到: " + outputDir);
        return 0;
    }
};

int main(int argc, char* argv[]) {
    try {
        DebugToolApplication app;
        
        if (!app.initialize()) {
            Utils::logError("应用程序初始化失败");
            return 1;
        }
        
        return app.run(argc, argv);
        
    } catch (const std::exception& e) {
        Utils::logError("应用程序异常: " + std::string(e.what()));
        return 1;
    } catch (...) {
        Utils::logError("未知异常");
        return 1;
    }
}

#else
int main() {
    std::cout << "此程序需要OpenCV支持，请重新编译并启用OpenCV" << std::endl;
    return 1;
}
#endif // USE_OPENCV

#include "../../include/batch_test_manager.h"
#include "../../include/common.h"
#ifdef USE_OPENCV
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <chrono>
#include <algorithm>
#include <sstream>

BatchTestManager::BatchTestManager() {
}

BatchTestManager::~BatchTestManager() {
}

bool BatchTestManager::initialize(std::shared_ptr<DamageDetectionEngine> engine,
                                 std::shared_ptr<VisualizationDebugger> debugger) {
    if (!engine) {
        Utils::logError("检测引擎指针为空");
        return false;
    }
    
    engine_ = engine;
    debugger_ = debugger;
    
    Utils::logInfo("批量测试管理器初始化完成");
    return true;
}

std::vector<BatchTestManager::TestResult> BatchTestManager::runBatchTest(
    const std::vector<std::string>& imagePaths,
    const std::string& outputDir,
    const std::string& testName) {
    
    std::vector<TestResult> results;
    results.reserve(imagePaths.size());
    
    // 创建输出目录
    Utils::createDirectory(outputDir);
    Utils::createDirectory(outputDir + "/images");
    
    Utils::logInfo("开始批量测试: " + testName + "，图像数量: " + std::to_string(imagePaths.size()));
    
    auto batchStartTime = std::chrono::high_resolution_clock::now();
    
    for (size_t i = 0; i < imagePaths.size(); ++i) {
        const std::string& imagePath = imagePaths[i];
        
        Utils::logInfo("处理图像 " + std::to_string(i + 1) + "/" + 
                      std::to_string(imagePaths.size()) + ": " + imagePath);
        
        TestResult result = processImage(imagePath);
        results.push_back(result);
        
        // 如果有可视化调试器，保存可视化结果
        if (debugger_ && result.success) {
            std::filesystem::path inputPath(imagePath);
            std::string outputImagePath = outputDir + "/images/" + 
                                        inputPath.stem().string() + "_result.jpg";
            debugger_->saveCurrentResult(outputImagePath);
        }
        
        // 显示进度
        if ((i + 1) % 10 == 0 || i == imagePaths.size() - 1) {
            double progress = static_cast<double>(i + 1) / imagePaths.size() * 100.0;
            Utils::logInfo("进度: " + std::to_string(static_cast<int>(progress)) + "%");
        }
    }
    
    auto batchEndTime = std::chrono::high_resolution_clock::now();
    double totalBatchTime = std::chrono::duration<double, std::milli>(batchEndTime - batchStartTime).count();
    
    // 计算统计信息
    BatchStatistics stats = calculateStatistics(results);
    
    // 生成报告
    std::string reportPath = outputDir + "/" + testName + "_report.html";
    generateTestReport(results, stats, reportPath);
    
    // 保存CSV结果
    std::string csvPath = outputDir + "/" + testName + "_results.csv";
    saveResultsToCSV(results, csvPath);
    
    Utils::logInfo("批量测试完成，总耗时: " + formatDuration(totalBatchTime));
    Utils::logInfo("成功处理: " + std::to_string(stats.successfulImages) + "/" + 
                  std::to_string(stats.totalImages) + " 张图像");
    Utils::logInfo("检测到损伤总数: " + std::to_string(stats.totalDamagesDetected));
    
    return results;
}

BatchTestManager::TestResult BatchTestManager::processImage(const std::string& imagePath) {
    TestResult result;
    result.imagePath = imagePath;
    
    try {
        // 加载图像
        cv::Mat image = cv::imread(imagePath);
        if (image.empty()) {
            result.success = false;
            result.errorMessage = "无法加载图像";
            return result;
        }
        
        // 执行检测
        auto startTime = std::chrono::high_resolution_clock::now();
        result.detectionResults = engine_->detectDamage(image, 0);
        auto endTime = std::chrono::high_resolution_clock::now();
        
        result.processingTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();
        result.success = true;
        
        // 如果有可视化调试器，处理图像以更新结果
        if (debugger_) {
            debugger_->processAndVisualize(image, 0);
        }
        
    } catch (const std::exception& e) {
        result.success = false;
        result.errorMessage = "处理异常: " + std::string(e.what());
        Utils::logError("处理图像失败 " + imagePath + ": " + result.errorMessage);
    }
    
    return result;
}

BatchTestManager::BatchStatistics BatchTestManager::calculateStatistics(
    const std::vector<TestResult>& results) {
    
    BatchStatistics stats;
    stats.reset();
    
    stats.totalImages = static_cast<int>(results.size());
    
    double totalConfidence = 0.0;
    int totalConfidenceCount = 0;
    std::map<DamageType, double> totalConfidenceByType;
    std::map<DamageType, int> confidenceCountByType;
    
    for (const auto& result : results) {
        if (result.success) {
            stats.successfulImages++;
            stats.totalProcessingTime += result.processingTime;
            stats.totalDamagesDetected += static_cast<int>(result.detectionResults.size());
            
            // 更新处理时间统计
            stats.minProcessingTime = std::min(stats.minProcessingTime, result.processingTime);
            stats.maxProcessingTime = std::max(stats.maxProcessingTime, result.processingTime);
            
            // 统计各类型损伤
            for (const auto& damage : result.detectionResults) {
                stats.damageTypeCounts[damage.type]++;
                totalConfidence += damage.confidence;
                totalConfidenceCount++;
                
                totalConfidenceByType[damage.type] += damage.confidence;
                confidenceCountByType[damage.type]++;
            }
        } else {
            stats.failedImages++;
        }
    }
    
    // 计算平均值
    if (stats.successfulImages > 0) {
        stats.averageProcessingTime = stats.totalProcessingTime / stats.successfulImages;
    }
    
    if (totalConfidenceCount > 0) {
        stats.averageConfidence = totalConfidence / totalConfidenceCount;
    }
    
    // 计算各类型平均置信度
    for (const auto& pair : totalConfidenceByType) {
        DamageType type = pair.first;
        double totalConf = pair.second;
        int count = confidenceCountByType[type];
        if (count > 0) {
            stats.averageConfidenceByType[type] = totalConf / count;
        }
    }
    
    return stats;
}

bool BatchTestManager::generateTestReport(const std::vector<TestResult>& results,
                                         const BatchStatistics& stats,
                                         const std::string& outputPath) {
    try {
        std::ofstream report(outputPath);
        if (!report.is_open()) {
            Utils::logError("无法创建报告文件: " + outputPath);
            return false;
        }
        
        writeHTMLReportHeader(report, "批量测试报告");
        
        // 写入概要信息
        report << "<div class='summary'>\n";
        report << "<h2>测试概要</h2>\n";
        report << "<p>生成时间: " << getCurrentTimestamp() << "</p>\n";
        report << "<p>总图像数: " << stats.totalImages << "</p>\n";
        report << "<p>成功处理: " << stats.successfulImages << "</p>\n";
        report << "<p>处理失败: " << stats.failedImages << "</p>\n";
        report << "<p>检测到损伤总数: " << stats.totalDamagesDetected << "</p>\n";
        report << "<p>平均处理时间: " << std::fixed << std::setprecision(2) 
               << stats.averageProcessingTime << " ms</p>\n";
        report << "<p>平均置信度: " << std::fixed << std::setprecision(3) 
               << stats.averageConfidence << "</p>\n";
        report << "</div>\n";
        
        // 写入统计表格
        writeStatisticsTable(report, stats);
        
        // 写入详细结果
        report << "<div class='details'>\n";
        report << "<h2>详细结果</h2>\n";
        report << "<table border='1'>\n";
        report << "<tr><th>图像</th><th>状态</th><th>处理时间(ms)</th><th>检测数量</th><th>损伤类型</th></tr>\n";
        
        for (const auto& result : results) {
            report << "<tr>\n";
            report << "<td>" << std::filesystem::path(result.imagePath).filename().string() << "</td>\n";
            report << "<td>" << (result.success ? "成功" : "失败") << "</td>\n";
            report << "<td>" << std::fixed << std::setprecision(2) << result.processingTime << "</td>\n";
            report << "<td>" << result.detectionResults.size() << "</td>\n";
            
            // 损伤类型统计
            std::map<DamageType, int> typeCounts;
            for (const auto& damage : result.detectionResults) {
                typeCounts[damage.type]++;
            }
            
            report << "<td>";
            bool first = true;
            for (const auto& pair : typeCounts) {
                if (!first) report << ", ";
                report << Utils::damageTypeToString(pair.first) << ":" << pair.second;
                first = false;
            }
            report << "</td>\n";
            report << "</tr>\n";
        }
        
        report << "</table>\n";
        report << "</div>\n";
        
        writeHTMLReportFooter(report);
        report.close();
        
        Utils::logInfo("测试报告已生成: " + outputPath);
        return true;
        
    } catch (const std::exception& e) {
        Utils::logError("生成测试报告失败: " + std::string(e.what()));
        return false;
    }
}

bool BatchTestManager::saveResultsToCSV(const std::vector<TestResult>& results,
                                        const std::string& outputPath) {
    try {
        std::ofstream csv(outputPath);
        if (!csv.is_open()) {
            Utils::logError("无法创建CSV文件: " + outputPath);
            return false;
        }
        
        // 写入CSV头部
        csv << "Image Path,Success,Processing Time (ms),Damage Count,";
        csv << "Crack Count,Wear Count,Scratch Count,Pit Count,Bulge Count,Aging Count,Install Damage Count,";
        csv << "Average Confidence,Error Message\n";
        
        // 写入数据
        for (const auto& result : results) {
            csv << "\"" << result.imagePath << "\",";
            csv << (result.success ? "TRUE" : "FALSE") << ",";
            csv << std::fixed << std::setprecision(2) << result.processingTime << ",";
            csv << result.detectionResults.size() << ",";
            
            // 统计各类型数量
            std::map<DamageType, int> typeCounts;
            double totalConfidence = 0.0;
            for (const auto& damage : result.detectionResults) {
                typeCounts[damage.type]++;
                totalConfidence += damage.confidence;
            }
            
            csv << typeCounts[DamageType::CRACK] << ",";
            csv << typeCounts[DamageType::WEAR] << ",";
            csv << typeCounts[DamageType::SCRATCH] << ",";
            csv << typeCounts[DamageType::PIT] << ",";
            csv << typeCounts[DamageType::BULGE] << ",";
            csv << typeCounts[DamageType::AGING] << ",";
            csv << typeCounts[DamageType::INSTALL_DAMAGE] << ",";
            
            double avgConfidence = result.detectionResults.empty() ? 0.0 : 
                                 totalConfidence / result.detectionResults.size();
            csv << std::fixed << std::setprecision(3) << avgConfidence << ",";
            csv << "\"" << result.errorMessage << "\"\n";
        }
        
        csv.close();
        Utils::logInfo("CSV结果已保存: " + outputPath);
        return true;
        
    } catch (const std::exception& e) {
        Utils::logError("保存CSV结果失败: " + std::string(e.what()));
        return false;
    }
}

// 辅助方法实现
std::vector<std::string> BatchTestManager::loadImagesFromDirectory(const std::string& directory,
                                                                  bool recursive) {
    std::vector<std::string> imageFiles;

    try {
        if (recursive) {
            for (const auto& entry : std::filesystem::recursive_directory_iterator(directory)) {
                if (entry.is_regular_file()) {
                    std::string filename = entry.path().string();
                    if (isImageFileStatic(filename)) {
                        imageFiles.push_back(filename);
                    }
                }
            }
        } else {
            for (const auto& entry : std::filesystem::directory_iterator(directory)) {
                if (entry.is_regular_file()) {
                    std::string filename = entry.path().string();
                    if (isImageFileStatic(filename)) {
                        imageFiles.push_back(filename);
                    }
                }
            }
        }

        // 排序文件名
        std::sort(imageFiles.begin(), imageFiles.end());

    } catch (const std::exception& e) {
        Utils::logError("读取目录失败: " + std::string(e.what()));
    }

    return imageFiles;
}

bool BatchTestManager::isImageFile(const std::string& filename) {
    return isImageFileStatic(filename);
}

bool BatchTestManager::isImageFileStatic(const std::string& filename) {
    std::string ext = std::filesystem::path(filename).extension().string();
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);

    return ext == ".jpg" || ext == ".jpeg" || ext == ".png" ||
           ext == ".bmp" || ext == ".tiff" || ext == ".tif";
}

std::string BatchTestManager::formatDuration(double milliseconds) {
    if (milliseconds < 1000) {
        return std::to_string(static_cast<int>(milliseconds)) + " ms";
    } else if (milliseconds < 60000) {
        return std::to_string(static_cast<int>(milliseconds / 1000)) + " s";
    } else {
        int minutes = static_cast<int>(milliseconds / 60000);
        int seconds = static_cast<int>((milliseconds - minutes * 60000) / 1000);
        return std::to_string(minutes) + "m " + std::to_string(seconds) + "s";
    }
}

std::string BatchTestManager::getCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

void BatchTestManager::writeHTMLReportHeader(std::ofstream& file, const std::string& title) {
    file << "<!DOCTYPE html>\n";
    file << "<html>\n<head>\n";
    file << "<meta charset='UTF-8'>\n";
    file << "<title>" << title << "</title>\n";
    file << "<style>\n";
    file << "body { font-family: Arial, sans-serif; margin: 20px; }\n";
    file << "h1, h2 { color: #333; }\n";
    file << "table { border-collapse: collapse; width: 100%; margin: 20px 0; }\n";
    file << "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n";
    file << "th { background-color: #f2f2f2; }\n";
    file << ".summary { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0; }\n";
    file << ".details { margin: 20px 0; }\n";
    file << "</style>\n";
    file << "</head>\n<body>\n";
    file << "<h1>" << title << "</h1>\n";
}

void BatchTestManager::writeHTMLReportFooter(std::ofstream& file) {
    file << "</body>\n</html>\n";
}

void BatchTestManager::writeStatisticsTable(std::ofstream& file, const BatchStatistics& stats) {
    file << "<div class='statistics'>\n";
    file << "<h2>统计信息</h2>\n";
    file << "<table>\n";
    file << "<tr><th>指标</th><th>值</th></tr>\n";
    file << "<tr><td>总图像数</td><td>" << stats.totalImages << "</td></tr>\n";
    file << "<tr><td>成功处理</td><td>" << stats.successfulImages << "</td></tr>\n";
    file << "<tr><td>处理失败</td><td>" << stats.failedImages << "</td></tr>\n";
    file << "<tr><td>检测到损伤总数</td><td>" << stats.totalDamagesDetected << "</td></tr>\n";
    file << "<tr><td>平均处理时间</td><td>" << std::fixed << std::setprecision(2)
         << stats.averageProcessingTime << " ms</td></tr>\n";
    file << "<tr><td>最小处理时间</td><td>" << std::fixed << std::setprecision(2)
         << stats.minProcessingTime << " ms</td></tr>\n";
    file << "<tr><td>最大处理时间</td><td>" << std::fixed << std::setprecision(2)
         << stats.maxProcessingTime << " ms</td></tr>\n";
    file << "<tr><td>平均置信度</td><td>" << std::fixed << std::setprecision(3)
         << stats.averageConfidence << "</td></tr>\n";
    file << "</table>\n";

    // 各类型损伤统计
    if (!stats.damageTypeCounts.empty()) {
        file << "<h3>各类型损伤统计</h3>\n";
        file << "<table>\n";
        file << "<tr><th>损伤类型</th><th>数量</th><th>平均置信度</th></tr>\n";

        for (const auto& pair : stats.damageTypeCounts) {
            DamageType type = pair.first;
            int count = pair.second;
            double avgConf = 0.0;

            auto it = stats.averageConfidenceByType.find(type);
            if (it != stats.averageConfidenceByType.end()) {
                avgConf = it->second;
            }

            file << "<tr><td>" << Utils::damageTypeToString(type) << "</td>";
            file << "<td>" << count << "</td>";
            file << "<td>" << std::fixed << std::setprecision(3) << avgConf << "</td></tr>\n";
        }

        file << "</table>\n";
    }

    file << "</div>\n";
}

// TestConfigManager实现
DamageDetectionEngine::AlgorithmParams TestConfigManager::getPresetConfig(PresetConfig preset) {
    DamageDetectionEngine::AlgorithmParams params;

    switch (preset) {
        case PresetConfig::HIGH_PRECISION:
            params.crackMinLength = 3.0;
            params.crackMaxWidth = 2.0;
            params.wearAreaThreshold = 50.0;
            params.dentMinArea = 25.0;
            params.bulgeMinArea = 25.0;
            break;

        case PresetConfig::HIGH_SPEED:
            params.crackMinLength = 10.0;
            params.crackMaxWidth = 5.0;
            params.wearAreaThreshold = 200.0;
            params.dentMinArea = 100.0;
            params.bulgeMinArea = 100.0;
            break;

        case PresetConfig::CRACK_FOCUSED:
            params.crackMinLength = 2.0;
            params.crackMaxWidth = 1.5;
            params.wearAreaThreshold = 500.0;  // 降低其他类型敏感度
            params.dentMinArea = 200.0;
            params.bulgeMinArea = 200.0;
            break;

        case PresetConfig::WEAR_FOCUSED:
            params.crackMinLength = 15.0;  // 降低裂缝敏感度
            params.crackMaxWidth = 5.0;
            params.wearAreaThreshold = 30.0;  // 提高磨损敏感度
            params.dentMinArea = 200.0;
            params.bulgeMinArea = 200.0;
            break;

        default: // DEFAULT
            // 使用默认值
            break;
    }

    return params;
}

BatchTestManager::ComparisonResult BatchTestManager::runComparisonTest(
    const std::vector<std::string>& imagePaths,
    const DamageDetectionEngine::AlgorithmParams& params1,
    const DamageDetectionEngine::AlgorithmParams& params2,
    const std::string& outputDir) {

    ComparisonResult comparison;
    comparison.testName1 = "Test1_Default";
    comparison.testName2 = "Test2_HighPrecision";

    Utils::logInfo("开始参数对比测试");
    Utils::createDirectory(outputDir);

    // 测试第一组参数
    Utils::logInfo("测试第一组参数...");
    engine_->setAlgorithmParams(params1);
    auto results1 = runBatchTest(imagePaths, outputDir + "/test1", comparison.testName1);
    comparison.stats1 = calculateStatistics(results1);

    // 测试第二组参数
    Utils::logInfo("测试第二组参数...");
    engine_->setAlgorithmParams(params2);
    auto results2 = runBatchTest(imagePaths, outputDir + "/test2", comparison.testName2);
    comparison.stats2 = calculateStatistics(results2);

    // 计算对比指标
    comparison = compareStatistics(comparison.stats1, comparison.stats2,
                                  comparison.testName1, comparison.testName2);

    // 生成对比报告
    std::string reportPath = outputDir + "/comparison_report.html";
    generateComparisonReport(comparison, reportPath);

    Utils::logInfo("参数对比测试完成");
    return comparison;
}

BatchTestManager::ComparisonResult BatchTestManager::compareStatistics(
    const BatchStatistics& stats1,
    const BatchStatistics& stats2,
    const std::string& name1,
    const std::string& name2) {

    ComparisonResult comparison;
    comparison.testName1 = name1;
    comparison.testName2 = name2;
    comparison.stats1 = stats1;
    comparison.stats2 = stats2;

    // 计算比率
    comparison.processingTimeRatio = stats1.averageProcessingTime > 0 ?
        stats2.averageProcessingTime / stats1.averageProcessingTime : 0.0;

    comparison.detectionCountRatio = stats1.totalDamagesDetected > 0 ?
        static_cast<double>(stats2.totalDamagesDetected) / stats1.totalDamagesDetected : 0.0;

    comparison.confidenceRatio = stats1.averageConfidence > 0 ?
        stats2.averageConfidence / stats1.averageConfidence : 0.0;

    // 计算各类型比率
    for (const auto& pair : stats1.damageTypeCounts) {
        DamageType type = pair.first;
        int count1 = pair.second;
        int count2 = 0;

        auto it = stats2.damageTypeCounts.find(type);
        if (it != stats2.damageTypeCounts.end()) {
            count2 = it->second;
        }

        comparison.typeCountRatios[type] = count1 > 0 ?
            static_cast<double>(count2) / count1 : 0.0;
    }

    return comparison;
}

bool BatchTestManager::generateComparisonReport(const ComparisonResult& comparison,
                                               const std::string& outputPath) {
    try {
        std::ofstream report(outputPath);
        if (!report.is_open()) {
            Utils::logError("无法创建对比报告文件: " + outputPath);
            return false;
        }

        writeHTMLReportHeader(report, "参数对比测试报告");

        // 写入对比概要
        report << "<div class='summary'>\n";
        report << "<h2>对比概要</h2>\n";
        report << "<p>生成时间: " << getCurrentTimestamp() << "</p>\n";
        report << "<p>测试1: " << comparison.testName1 << "</p>\n";
        report << "<p>测试2: " << comparison.testName2 << "</p>\n";
        report << "</div>\n";

        // 写入对比表格
        writeComparisonTable(report, comparison);

        // 写入详细统计
        report << "<div class='details'>\n";
        report << "<h2>详细统计对比</h2>\n";

        report << "<h3>" << comparison.testName1 << " 统计</h3>\n";
        writeStatisticsTable(report, comparison.stats1);

        report << "<h3>" << comparison.testName2 << " 统计</h3>\n";
        writeStatisticsTable(report, comparison.stats2);

        report << "</div>\n";

        writeHTMLReportFooter(report);
        report.close();

        Utils::logInfo("对比报告已生成: " + outputPath);
        return true;

    } catch (const std::exception& e) {
        Utils::logError("生成对比报告失败: " + std::string(e.what()));
        return false;
    }
}

void BatchTestManager::writeComparisonTable(std::ofstream& file, const ComparisonResult& comparison) {
    file << "<div class='comparison'>\n";
    file << "<h2>对比结果</h2>\n";
    file << "<table>\n";
    file << "<tr><th>指标</th><th>" << comparison.testName1 << "</th><th>"
         << comparison.testName2 << "</th><th>比率 (Test2/Test1)</th></tr>\n";

    file << "<tr><td>平均处理时间 (ms)</td>";
    file << "<td>" << std::fixed << std::setprecision(2) << comparison.stats1.averageProcessingTime << "</td>";
    file << "<td>" << std::fixed << std::setprecision(2) << comparison.stats2.averageProcessingTime << "</td>";
    file << "<td>" << std::fixed << std::setprecision(3) << comparison.processingTimeRatio << "</td></tr>\n";

    file << "<tr><td>检测到损伤总数</td>";
    file << "<td>" << comparison.stats1.totalDamagesDetected << "</td>";
    file << "<td>" << comparison.stats2.totalDamagesDetected << "</td>";
    file << "<td>" << std::fixed << std::setprecision(3) << comparison.detectionCountRatio << "</td></tr>\n";

    file << "<tr><td>平均置信度</td>";
    file << "<td>" << std::fixed << std::setprecision(3) << comparison.stats1.averageConfidence << "</td>";
    file << "<td>" << std::fixed << std::setprecision(3) << comparison.stats2.averageConfidence << "</td>";
    file << "<td>" << std::fixed << std::setprecision(3) << comparison.confidenceRatio << "</td></tr>\n";

    file << "</table>\n";

    // 各类型对比
    if (!comparison.typeCountRatios.empty()) {
        file << "<h3>各类型损伤对比</h3>\n";
        file << "<table>\n";
        file << "<tr><th>损伤类型</th><th>" << comparison.testName1 << "</th><th>"
             << comparison.testName2 << "</th><th>比率</th></tr>\n";

        for (const auto& pair : comparison.typeCountRatios) {
            DamageType type = pair.first;
            double ratio = pair.second;

            int count1 = 0, count2 = 0;
            auto it1 = comparison.stats1.damageTypeCounts.find(type);
            if (it1 != comparison.stats1.damageTypeCounts.end()) {
                count1 = it1->second;
            }

            auto it2 = comparison.stats2.damageTypeCounts.find(type);
            if (it2 != comparison.stats2.damageTypeCounts.end()) {
                count2 = it2->second;
            }

            file << "<tr><td>" << Utils::damageTypeToString(type) << "</td>";
            file << "<td>" << count1 << "</td>";
            file << "<td>" << count2 << "</td>";
            file << "<td>" << std::fixed << std::setprecision(3) << ratio << "</td></tr>\n";
        }

        file << "</table>\n";
    }

    file << "</div>\n";
}

#endif // USE_OPENCV

#include "../../include/rtsp_stream_manager.h"
#include "../../include/video_encoder.h"
#include "../../include/stream_client.h"
#include "../../include/common.h"
#include <chrono>
#include <thread>
#include <future>

RTSPStreamManager::RTSPStreamManager() 
    : status_(StreamStatus::STOPPED)
    , initialized_(false)
    , shouldStop_(false) {
    stats_.reset();
}

RTSPStreamManager::~RTSPStreamManager() {
    stop();
}

bool RTSPStreamManager::initialize(const RTSPStreamConfig& config) {
    if (initialized_) {
        logError("推流管理器已经初始化");
        return false;
    }
    
    config_ = config;
    
    // 创建视频编码器
    encoder_ = std::make_unique<VideoEncoder>();
    VideoEncoderConfig encoderConfig;
    encoderConfig.codec = config_.codec;
    encoderConfig.width = config_.width;
    encoderConfig.height = config_.height;
    encoderConfig.fps = config_.fps;
    encoderConfig.bitrate = config_.bitrate;
    encoderConfig.preset = config_.preset;
    encoderConfig.profile = "baseline"; // 实时推流使用baseline
    encoderConfig.threads = config_.encoderThreads;
    encoderConfig.enableHardwareAccel = config_.enableHardwareAccel;
    
    if (!encoder_->initialize(encoderConfig)) {
        logError("视频编码器初始化失败");
        return false;
    }
    
    // 创建推流客户端
    client_ = std::make_unique<StreamClient>();
    StreamClientConfig clientConfig;

    // 从配置管理器获取推流配置
    const auto& configManager = ConfigManager::getInstance();

    clientConfig.enabled = config_.enabled;
    clientConfig.pushUrl = configManager.getStreamingPushUrl();
    clientConfig.viewUrl = configManager.getStreamingViewUrl();

    // 验证推流配置
    if (!validateStreamingConfig(clientConfig.pushUrl, clientConfig.viewUrl)) {
        logError("推流配置验证失败，请检查配置文件");
        return false;
    }
    clientConfig.codec = config_.codec;
    clientConfig.width = config_.width;
    clientConfig.height = config_.height;
    clientConfig.fps = config_.fps;
    clientConfig.bitrate = config_.bitrate;
    clientConfig.preset = config_.preset;
    clientConfig.maxRetries = configManager.getMaxRetries();
    clientConfig.reconnectIntervalMs = configManager.getReconnectIntervalMs();
    clientConfig.bufferSize = config_.bufferSize;
    clientConfig.maxQueueSize = config_.bufferSize;
    
    if (!client_->initialize(clientConfig)) {
        logError("推流客户端初始化失败");
        return false;
    }
    
    initialized_ = true;
    status_ = StreamStatus::STOPPED;
    stats_.reset();
    
    logInfo("推流管理器初始化成功: " + getRTSPUrl());
    return true;
}

bool RTSPStreamManager::start() {
    if (!initialized_) {
        logError("推流管理器未初始化");
        return false;
    }
    
    if (status_ == StreamStatus::RUNNING) {
        logInfo("推流管理器已在运行");
        return true;
    }
    
    status_ = StreamStatus::STARTING;
    
    // 连接推流客户端
    if (!client_->connect()) {
        logError("推流客户端连接失败");
        status_ = StreamStatus::ERROR;
        return false;
    }
    
    // 启动推流工作线程
    shouldStop_ = false;
    streamThread_ = std::make_unique<std::thread>(&RTSPStreamManager::streamWorker, this);
    
    status_ = StreamStatus::RUNNING;
    stats_.reset();
    stats_.startTime = std::chrono::steady_clock::now();
    
    logInfo("推流管理器启动成功");
    return true;
}

void RTSPStreamManager::stop() {
    if (status_ == StreamStatus::STOPPED) {
        return;
    }

    logInfo("开始停止推流管理器...");
    status_ = StreamStatus::STOPPING;
    shouldStop_ = true;

    // 通知工作线程停止
    frameCondition_.notify_all();

    // 等待工作线程结束，设置超时机制
    if (streamThread_ && streamThread_->joinable()) {
        logInfo("等待推流线程结束...");

        // 使用超时机制避免无限等待
        auto future = std::async(std::launch::async, [this]() {
            if (streamThread_->joinable()) {
                streamThread_->join();
            }
        });

        // 等待最多3秒
        if (future.wait_for(std::chrono::seconds(3)) == std::future_status::timeout) {
            logError("推流线程未能在3秒内正常结束，强制继续");
            // 注意：这里不能强制终止线程，只能继续执行清理
        } else {
            logInfo("推流线程已正常结束");
        }

        streamThread_.reset();
    }

    // 断开推流客户端
    if (client_) {
        logInfo("断开推流客户端连接...");
        client_->disconnect();
    }

    // 清理帧队列
    {
        std::lock_guard<std::mutex> lock(frameMutex_);
        while (!frameQueue_.empty()) {
            frameQueue_.pop();
        }
    }

    status_ = StreamStatus::STOPPED;
    logInfo("推流管理器已停止");
}

bool RTSPStreamManager::pushFrame(const cv::Mat& frame, int cameraId) {
    if (!isRunning() || frame.empty()) {
        return false;
    }

    // 质量控制：检查帧率
    auto now = std::chrono::steady_clock::now();
    auto timeSinceLastFrame = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastFrameTime_).count();
    int targetInterval = 1000 / config_.fps;

    // 如果帧率过高，跳过此帧
    if (timeSinceLastFrame < targetInterval * 0.8) {
        return true; // 跳过但返回成功
    }

    // 质量控制：检查队列大小，实现自适应丢帧
    {
        std::lock_guard<std::mutex> lock(frameMutex_);
        double queueRatio = static_cast<double>(frameQueue_.size()) / config_.bufferSize;

        // 如果队列使用率超过阈值，开始丢帧
        if (queueRatio > 0.8) {
            // 丢弃一些旧帧以减少延迟
            int framesToDrop = frameQueue_.size() / 3;
            for (int i = 0; i < framesToDrop && !frameQueue_.empty(); ++i) {
                frameQueue_.pop();
                stats_.droppedFrames++;
            }
        }

        // 检查帧尺寸并调整
        cv::Mat processedFrame;
        if (frame.cols != config_.width || frame.rows != config_.height) {
            cv::resize(frame, processedFrame, cv::Size(config_.width, config_.height));
        } else {
            processedFrame = frame.clone();
        }

        // 添加到队列
        if (frameQueue_.size() < config_.bufferSize) {
            frameQueue_.push(processedFrame);
        } else {
            // 队列满，丢弃最旧的帧
            frameQueue_.pop();
            frameQueue_.push(processedFrame);
            stats_.droppedFrames++;
        }
    }

    frameCondition_.notify_one();
    stats_.totalFrames++;
    lastFrameTime_ = now;

    return true;
}

bool RTSPStreamManager::updateConfig(const RTSPStreamConfig& config) {
    if (isRunning()) {
        logError("无法在运行状态下更新配置");
        return false;
    }
    
    config_ = config;
    
    // 重新初始化组件
    cleanup();
    initialized_ = false;
    
    return initialize(config);
}

std::string RTSPStreamManager::getRTSPUrl() const {
    return "rtsp://" + config_.serverAddress + ":" + std::to_string(config_.port) + config_.streamPath;
}

void RTSPStreamManager::streamWorker() {
    logInfo("推流工作线程启动");
    
    while (!shouldStop_) {
        cv::Mat frame;
        
        // 获取帧数据
        {
            std::unique_lock<std::mutex> lock(frameMutex_);
            frameCondition_.wait_for(lock, std::chrono::milliseconds(100),
                                   [this] { return !frameQueue_.empty() || shouldStop_; });
            
            if (shouldStop_) {
                break;
            }
            
            if (frameQueue_.empty()) {
                continue;
            }
            
            frame = frameQueue_.front();
            frameQueue_.pop();
        }
        
        // 处理帧
        if (!processFrame(frame)) {
            stats_.droppedFrames++;

            // 检查推流客户端状态，如果处于错误状态则减少日志输出
            if (client_ && client_->getStatus() == StreamClientStatus::ERROR) {
                static int errorLogCount = 0;
                errorLogCount++;
                if (errorLogCount % 100 == 1) { // 每100次错误记录一次日志
                    logError("推流客户端处于错误状态，暂停推流 (错误次数: " + std::to_string(errorLogCount) + ")");
                }
            } else {
                logError("处理帧失败");
            }
        }
        
        updateStats();
    }
    
    logInfo("推流工作线程结束");
}

bool RTSPStreamManager::processFrame(const cv::Mat& frame) {
    if (!encoder_ || !client_) {
        return false;
    }

    // 检查推流客户端状态
    auto clientStatus = client_->getStatus();
    if (clientStatus == StreamClientStatus::ERROR) {
        // 推流客户端处于错误状态，跳过处理但不报错
        return true; // 返回true避免大量错误日志
    }

    // 编码帧
    EncodedPacket packet;
    if (!encoder_->encode(frame, packet)) {
        return false;
    }

    // 如果没有编码输出（需要更多输入帧），返回成功
    if (packet.size == 0) {
        return true;
    }

    // 发送编码数据包
    if (!client_->sendPacket(packet)) {
        // 发送失败，但不一定是严重错误（可能是网络问题）
        return false;
    }

    stats_.encodedFrames++;
    stats_.totalBytes += packet.size;

    return true;
}

void RTSPStreamManager::updateStats() {
    auto now = std::chrono::steady_clock::now();
    auto timeDiff = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastFrameTime_).count();

    if (timeDiff > 0) {
        stats_.currentFPS = 1000.0 / timeDiff;
    }

    lastFrameTime_ = now;

    // 计算平均码率
    auto totalTime = std::chrono::duration_cast<std::chrono::seconds>(now - stats_.startTime).count();
    if (totalTime > 0) {
        stats_.avgBitrate = (stats_.totalBytes * 8.0) / totalTime;
    }

    // 更新客户端数量（对于外部服务器，这个值通常为1或0）
    if (client_ && client_->isConnected()) {
        stats_.currentClients = 1;
    } else {
        stats_.currentClients = 0;
    }

    // 自适应质量控制
    performQualityControl();
}

void RTSPStreamManager::performQualityControl() {
    if (!encoder_ || !client_) {
        return;
    }

    // 检查丢帧率
    uint64_t totalFrames = stats_.totalFrames.load();
    uint64_t droppedFrames = stats_.droppedFrames.load();

    if (totalFrames > 100) { // 有足够的样本数据
        double dropRate = static_cast<double>(droppedFrames) / totalFrames;

        // 如果丢帧率过高，降低码率
        if (dropRate > 0.1) { // 丢帧率超过10%
            int currentBitrate = encoder_->getCurrentBitrate();
            int newBitrate = static_cast<int>(currentBitrate * 0.8); // 降低20%

            // 确保不低于最小码率
            if (newBitrate < 500000) { // 最小500kbps
                newBitrate = 500000;
            }

            if (newBitrate != currentBitrate) {
                encoder_->adjustBitrate(newBitrate);
                logInfo("由于丢帧率过高(" + std::to_string(dropRate * 100) +
                       "%)，码率调整为: " + std::to_string(newBitrate / 1000) + " kbps");
            }
        }
        // 如果丢帧率很低且连接稳定，可以适当提高码率
        else if (dropRate < 0.02 && client_->isConnected()) { // 丢帧率低于2%
            int currentBitrate = encoder_->getCurrentBitrate();
            int maxBitrate = config_.bitrate; // 不超过配置的最大码率

            if (currentBitrate < maxBitrate) {
                int newBitrate = static_cast<int>(currentBitrate * 1.1); // 提高10%
                if (newBitrate > maxBitrate) {
                    newBitrate = maxBitrate;
                }

                if (newBitrate != currentBitrate) {
                    encoder_->adjustBitrate(newBitrate);
                    logInfo("连接稳定，码率调整为: " + std::to_string(newBitrate / 1000) + " kbps");
                }
            }
        }
    }
}

void RTSPStreamManager::cleanup() {
    encoder_.reset();
    client_.reset();
}

bool RTSPStreamManager::validateStreamingConfig(const std::string& pushUrl, const std::string& viewUrl) const {
    // 验证URL格式
    if (pushUrl.empty() || viewUrl.empty()) {
        logError("推流URL或观看URL为空");
        return false;
    }

    // 验证推流URL协议
    if (pushUrl.find("rtsp://") != 0 && pushUrl.find("rtmp://") != 0) {
        logError("不支持的推流协议，仅支持RTSP和RTMP: " + pushUrl);
        return false;
    }

    // 验证观看URL协议
    if (viewUrl.find("rtsp://") != 0 && viewUrl.find("http://") != 0) {
        logError("不支持的观看协议，仅支持RTSP和HTTP: " + viewUrl);
        return false;
    }

    // 验证URL格式的基本有效性
    if (pushUrl.find("://") == std::string::npos || viewUrl.find("://") == std::string::npos) {
        logError("URL格式无效");
        return false;
    }

    // 提取主机和端口信息进行基本验证
    size_t hostStart = pushUrl.find("://") + 3;
    size_t hostEnd = pushUrl.find("/", hostStart);
    if (hostEnd == std::string::npos) hostEnd = pushUrl.length();

    std::string hostPort = pushUrl.substr(hostStart, hostEnd - hostStart);
    if (hostPort.empty()) {
        logError("推流URL中缺少主机信息: " + pushUrl);
        return false;
    }

    logInfo("推流配置验证通过 - 推流URL: " + pushUrl + ", 观看URL: " + viewUrl);
    return true;
}

void RTSPStreamManager::logError(const std::string& message) const {
    Utils::logError("RTSPStreamManager: " + message);
}

void RTSPStreamManager::logInfo(const std::string& message) const {
    Utils::logInfo("RTSPStreamManager: " + message);
}

#include <opencv2/opencv.hpp>
#include <iostream>
#include <cstdlib>

/**
 * @brief 简单的OpenCV GUI测试程序
 * 用于验证OpenCV GUI功能是否正常工作
 */
int main() {
    std::cout << "=== OpenCV GUI测试程序 ===" << std::endl;
    
    // 显示环境信息
    std::string display = std::getenv("DISPLAY") ? std::getenv("DISPLAY") : "未设置";
    std::string waylandDisplay = std::getenv("WAYLAND_DISPLAY") ? std::getenv("WAYLAND_DISPLAY") : "未设置";
    std::string sessionType = std::getenv("XDG_SESSION_TYPE") ? std::getenv("XDG_SESSION_TYPE") : "未设置";
    
    std::cout << "显示环境:" << std::endl;
    std::cout << "  DISPLAY = " << display << std::endl;
    std::cout << "  WAYLAND_DISPLAY = " << waylandDisplay << std::endl;
    std::cout << "  XDG_SESSION_TYPE = " << sessionType << std::endl;
    std::cout << std::endl;
    
    // 显示OpenCV版本
    std::cout << "OpenCV版本: " << CV_VERSION << std::endl;
    std::cout << std::endl;
    
    try {
        // 创建一个简单的测试图像
        cv::Mat testImage = cv::Mat::zeros(400, 600, CV_8UC3);
        
        // 绘制一些内容
        cv::putText(testImage, "OpenCV GUI Test", cv::Point(150, 100), 
                   cv::FONT_HERSHEY_SIMPLEX, 1.0, cv::Scalar(0, 255, 0), 2);
        cv::putText(testImage, "If you can see this window,", cv::Point(120, 150), 
                   cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(255, 255, 255), 1);
        cv::putText(testImage, "OpenCV GUI is working!", cv::Point(150, 180), 
                   cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(255, 255, 255), 1);
        cv::putText(testImage, "Press any key to exit", cv::Point(170, 250), 
                   cv::FONT_HERSHEY_SIMPLEX, 0.6, cv::Scalar(100, 200, 255), 1);
        
        // 绘制一个矩形
        cv::rectangle(testImage, cv::Point(50, 300), cv::Point(550, 350), 
                     cv::Scalar(255, 0, 0), 2);
        cv::putText(testImage, "Test Rectangle", cv::Point(220, 330), 
                   cv::FONT_HERSHEY_SIMPLEX, 0.6, cv::Scalar(255, 0, 0), 1);
        
        std::cout << "尝试创建窗口..." << std::endl;
        
        // 创建窗口
        cv::namedWindow("OpenCV GUI Test", cv::WINDOW_AUTOSIZE);
        std::cout << "窗口创建成功" << std::endl;
        
        // 显示图像
        cv::imshow("OpenCV GUI Test", testImage);
        std::cout << "图像显示成功" << std::endl;
        
        // 检查窗口属性
        try {
            double visible = cv::getWindowProperty("OpenCV GUI Test", cv::WND_PROP_VISIBLE);
            std::cout << "窗口可见性属性: " << visible << std::endl;
        } catch (const cv::Exception& e) {
            std::cout << "窗口属性检查异常: " << e.what() << std::endl;
        }
        
        std::cout << std::endl;
        std::cout << "✓ GUI测试成功！" << std::endl;
        std::cout << "如果您能看到测试窗口，说明OpenCV GUI功能正常" << std::endl;
        std::cout << "按任意键退出..." << std::endl;
        
        // 等待按键
        cv::waitKey(0);
        
        // 清理
        cv::destroyAllWindows();
        
        std::cout << "测试完成" << std::endl;
        return 0;
        
    } catch (const cv::Exception& e) {
        std::cout << "✗ OpenCV GUI错误: " << e.what() << std::endl;
        std::cout << std::endl;
        std::cout << "可能的解决方案:" << std::endl;
        std::cout << "1. 安装GUI依赖: sudo apt install libgtk-3-dev libgtk2.0-dev" << std::endl;
        std::cout << "2. 如果使用Wayland: export GDK_BACKEND=x11" << std::endl;
        std::cout << "3. 重新编译OpenCV with GUI support" << std::endl;
        return -1;
    } catch (const std::exception& e) {
        std::cout << "✗ 其他错误: " << e.what() << std::endl;
        return -1;
    }
}
